<template>
  <div class="app-container">
    <el-form :model="listQuery" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="订单号" prop="orderSn">
        <el-input
          v-model="listQuery.orderSn"
          placeholder="请输入订单号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="果树名称" prop="fruitTreeName">
        <el-input
          v-model="listQuery.fruitTreeName"
          placeholder="请输入果树名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input
          v-model="listQuery.contactName"
          placeholder="请输入联系人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select
          v-model="listQuery.status"
          placeholder="订单状态"
          clearable
          style="width: 240px"
        >
          <el-option label="待付款" :value="0" />
          <el-option label="待发货" :value="10" />
          <el-option label="已发货" :value="20" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list">

      <el-table-column align="center" label="订单号" prop="orderSn" min-width="180" show-overflow-tooltip />

      <el-table-column align="center" label="果树名称" prop="fruitTreeName" min-width="150" show-overflow-tooltip />

      <el-table-column align="center" label="联系人" prop="contactName" width="100" />

      <el-table-column align="center" label="手机号" prop="phone" width="120" />

      <el-table-column align="center" label="收货地址" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.province }}{{ scope.row.city }}{{ scope.row.district }}{{ scope.row.detailAddress }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="总价" prop="totalPrice" width="100">
        <template slot-scope="scope">
          ¥{{ scope.row.totalPrice }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="订单状态" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="支付状态" prop="payStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.payStatus" :type="scope.row.payStatus === 'PAID' ? 'success' : 'warning'">
            {{ scope.row.payStatus === 'PAID' ? '已付款' : '待付款' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="认养时间" prop="adoptTime" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.adoptTime, '{y}-{m}-{d} {h}:{i}') }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="280" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button v-if="scope.row.status == 10" size="mini" type="text" @click="handleDelivery(scope.row)">发货</el-button>
          <el-button size="mini" type="text" @click="handleCertificate(scope.row)">证书</el-button>
          <el-button size="mini" type="text" @click="handleRights(scope.row)">权益</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ detailData.orderSn }}</el-descriptions-item>
        <el-descriptions-item label="果树名称">{{ detailData.fruitTreeName }}</el-descriptions-item>
        <el-descriptions-item label="单价">¥{{ detailData.unitPrice }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ detailData.fruitNum }}</el-descriptions-item>
        <el-descriptions-item label="运费">¥{{ detailData.freightPrice }}</el-descriptions-item>
        <el-descriptions-item label="总价">¥{{ detailData.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ getStatusText(detailData.status) }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">{{ detailData.payStatus === 'PAID' ? '已付款' : '待付款' }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ detailData.paymentMethod || '-' }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(detailData.paymentTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailData.contactName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detailData.phone }}</el-descriptions-item>
        <el-descriptions-item label="收货地址" :span="2">
          {{ detailData.province }}{{ detailData.city }}{{ detailData.district }}{{ detailData.detailAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="标签名称">{{ detailData.labelName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="心愿寄语">{{ detailData.wishMsg || '-' }}</el-descriptions-item>
        <el-descriptions-item label="认养时间">{{ parseTime(detailData.adoptTime) }}</el-descriptions-item>
        <el-descriptions-item label="认养人">{{ detailData.adoptBy }}</el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ parseTime(detailData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailData.updateTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 发货对话框 -->
    <el-dialog title="订单发货" :visible.sync="deliveryVisible" width="500px" append-to-body>
      <el-form ref="deliveryForm" :model="deliveryForm" :rules="deliveryRules" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="deliveryForm.orderSn" disabled />
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCode">
          <el-select v-model="deliveryForm.logisticsCode" placeholder="请选择物流公司" style="width: 100%" @change="handleLogisticsChange">
            <el-option
              v-for="item in logisticsList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="logisticsNo">
          <el-input v-model="deliveryForm.logisticsNo" placeholder="请输入物流单号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deliveryVisible = false">取消</el-button>
        <el-button type="primary" @click="submitDelivery">确定</el-button>
      </div>
    </el-dialog>

    <!-- 证书详情弹窗 -->
    <el-dialog title="认养证书" :visible.sync="certificateVisible" width="600px" append-to-body>
      <div class="certificate-content">
        <div class="certificate-header">
          <h2>认养证书</h2>
          <p class="certificate-id">证书编号：{{ certificateData.orderSn || certificateData.id }}</p>
        </div>

        <div class="adoptee-name">
          <span>{{ certificateData.adoptBy || '认养用户' }}</span>
          <div class="name-underline"></div>
        </div>

        <div class="certificate-body">
          <p class="cert-paragraph">
            恭喜您，成功认养了位于
            <strong>{{ certificateLocation }}</strong> 的
            <strong>{{ certificateData.fruitTreeName || '果树' }}</strong>
            编号为 <span class="highlight">{{ certificateData.orderSn || certificateData.id }}</span>。
          </p>

          <p class="cert-paragraph">
            {{ certificateContent }}
          </p>

          <div class="cert-details">
            <p><strong>标签名称：</strong>{{ certificateData.labelName || '-' }}</p>
            <p><strong>心愿寄语：</strong>{{ certificateData.wishMsg || '-' }}</p>
            <p><strong>认养时间：</strong>{{ parseTime(certificateData.adoptTime) }}</p>
          </div>
        </div>

        <div class="certificate-footer">
          <p>{{ certificateIssuer }}</p>
          <p>{{ parseTime(certificateData.adoptTime, '{y}年{m}月{d}日') }}</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="certificateVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 权益详情弹窗 -->
    <el-dialog title="认养权益" :visible.sync="rightsVisible" width="600px" append-to-body>
      <div class="rights-content">
        <h3>您的认养权益包括：</h3>
        <div class="rights-list">
          <div v-if="rightsData.benefits" class="benefit-item">
            <h4>基础权益</h4>
            <p>{{ rightsData.benefits }}</p>
          </div>
          <div class="benefit-item">
            <h4>订单信息</h4>
            <p><strong>果树名称：</strong>{{ rightsData.fruitTreeName }}</p>
            <p><strong>认养数量：</strong>{{ rightsData.fruitNum }}棵</p>
            <p><strong>认养期限：</strong>1年</p>
            <p><strong>预计产量：</strong>根据果树品种和生长情况而定</p>
          </div>
          <div class="benefit-item">
            <h4>服务承诺</h4>
            <p>• 专属标识牌，标注您的姓名</p>
            <p>• 定期生长状态更新</p>
            <p>• 成熟期免费采摘体验</p>
            <p>• 果实成熟后免费配送</p>
            <p>• 专业技术指导和咨询</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rightsVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listFruitOrder, getFruitOrder, deliveryOrder } from '@/api/fruit/order'
import { getDicts } from '@/api/system/dict/data'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'FruitOrderList',
  components: { Pagination },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      list: [],
      // 查询参数
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        orderSn: undefined,
        fruitTreeName: undefined,
        contactName: undefined,
        status: undefined
      },
      detailVisible: false,
      detailData: {},
      deliveryVisible: false,
      certificateVisible: false,
      certificateData: {},
      rightsVisible: false,
      rightsData: {},
      deliveryForm: {
        orderSn: '',
        logisticsCode: '',
        logisticsNo: '',
        logisticsName: ''
      },
      deliveryRules: {
        logisticsCode: [
          { required: true, message: '请选择物流公司', trigger: 'change' }
        ],
        logisticsNo: [
          { required: true, message: '请输入物流单号', trigger: 'blur' }
        ]
      },
      logisticsList: []
    }
  },
  created() {
    this.getList()
    this.getLogisticsList()
  },
  methods: {
    parseTime,
    /** 查询订单列表 */
    getList() {
      this.loading = true
      listFruitOrder(this.listQuery).then(response => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.loading = false
      })
    },
    getLogisticsList() {
      getDicts('logistics_company').then(response => {
        console.log('物流公司数据:', response)
        this.logisticsList = response.data || []
      }).catch(error => {
        console.error('获取物流公司列表失败:', error)
        this.$message.error('获取物流公司列表失败')
        // 如果字典数据获取失败，使用默认的物流公司列表
        this.logisticsList = [
          { dictValue: 'SF', dictLabel: '顺丰速运' },
          { dictValue: 'YTO', dictLabel: '圆通速递' },
          { dictValue: 'ZTO', dictLabel: '中通快递' },
          { dictValue: 'STO', dictLabel: '申通快递' },
          { dictValue: 'YD', dictLabel: '韵达速递' },
          { dictValue: 'HTKY', dictLabel: '百世汇通' },
          { dictValue: 'JD', dictLabel: '京东物流' },
          { dictValue: 'EMS', dictLabel: '中国邮政' }
        ]
      })
    },
    /** 搜索按钮操作 */
    handleFilter() {
      this.listQuery.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleFilter()
    },
    handleDetail(row) {
      getFruitOrder(row.id).then(response => {
        this.detailData = response.data
        this.detailVisible = true
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },
    handleDelivery(row) {
      this.deliveryForm = {
        orderSn: row.orderSn,
        logisticsCode: '',
        logisticsNo: '',
        logisticsName: ''
      }
      this.deliveryVisible = true
    },
    handleLogisticsChange(value) {
      const logistics = this.logisticsList.find(item => item.dictValue === value)
      if (logistics) {
        this.deliveryForm.logisticsName = logistics.dictLabel
      }
    },
    submitDelivery() {
      this.$refs.deliveryForm.validate(valid => {
        if (valid) {
          deliveryOrder(this.deliveryForm).then(() => {
            this.$message.success('发货成功')
            this.deliveryVisible = false
            this.getList()
          }).catch(() => {
            this.$message.error('发货失败')
          })
        }
      })
    },
    getStatusText(status) {
      const statusMap = {
        0: '待付款',
        10: '待发货',
        20: '已发货'
      }
      return statusMap[status] || '未知'
    },
    getStatusType(status) {
      const typeMap = {
        0: 'warning',
        10: 'success',
        20: 'primary'
      }
      return typeMap[status] || 'info'
    },
    handleCertificate(row) {
      // 查看证书详情
      getFruitOrder(row.id).then(response => {
        this.certificateData = response.data
        this.certificateVisible = true
      }).catch(() => {
        this.$message.error('获取证书信息失败')
      })
    },
    handleRights(row) {
      // 查看权益详情
      getFruitOrder(row.id).then(response => {
        this.rightsData = response.data
        this.rightsVisible = true
      }).catch(() => {
        this.$message.error('获取权益信息失败')
      })
    }
  },
  computed: {
    // 证书地点
    certificateLocation() {
      return this.certificateData?.certOrigin || '陕西延安'
    },
    // 证书内容
    certificateContent() {
      return this.certificateData?.certContent || '绿荫泽被，生生不息。您的认养善举，不仅赋予了这棵树木特别的守护，更是为城市增添一抹生机，为大地播撒一份希望。感谢您对绿色家园的深情厚意，您的行动让我们的环境更加清新美好。'
    },
    // 发证机构
    certificateIssuer() {
      return this.certificateData?.certIssuerName || '金壶口生态基金会'
    }
  }
}
</script>

<style scoped>
.certificate-content {
  text-align: center;
  padding: 30px 20px;
  line-height: 1.8;
}

.certificate-header h2 {
  color: #dd3c29;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.certificate-id {
  color: #dd3c29;
  font-size: 12px;
  margin-bottom: 30px;
}

.adoptee-name {
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.adoptee-name span {
  font-size: 18px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 15px;
}

.name-underline {
  width: 200px;
  height: 1px;
  background-color: rgba(221, 60, 41, 0.5);
}

.certificate-body {
  text-align: left;
  margin: 30px 0;
  color: #3d3d3d;
}

.cert-paragraph {
  margin-bottom: 20px;
  line-height: 2;
  text-align: justify;
}

.cert-paragraph strong {
  font-weight: bold;
}

.cert-paragraph .highlight {
  color: #dd3c29;
  font-weight: bold;
}

.cert-details {
  margin: 30px 0;
}

.cert-details p {
  margin: 8px 0;
  line-height: 1.6;
}

.certificate-footer {
  margin-top: 40px;
  color: #666;
  font-size: 14px;
  text-align: left;
}

.certificate-footer p {
  margin: 5px 0;
}

.rights-content h3 {
  color: #409EFF;
  margin-bottom: 20px;
}

.rights-list {
  text-align: left;
}

.benefit-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.benefit-item h4 {
  color: #333;
  margin-bottom: 10px;
}

.benefit-item p {
  margin: 5px 0;
  line-height: 1.6;
}
</style>


